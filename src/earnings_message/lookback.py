import pandas as pd
from src.earnings_message.schemas import QuarterlyData, EightKFilingsData
from src.database.factory import DatabaseFactory
from src.earnings_message.utils import calculate_growth, calculate_bps_change

connection = DatabaseFactory.get_mongo_connection()
eight_k_collection = connection.get_filings_collection("eight_k_filings")


def lookback_data(data: EightKFilingsData) -> tuple[QuarterlyData, QuarterlyData, QuarterlyData]:
    current_period = pd.Period(f"{data['year']}{data['quarter']}") # Q2 2025
    q_minus_1 = current_period - 1 # Q1 2025
    q_minus_2 = current_period - 2 # Q4 2024
    y_minus_1 = current_period - 4 # Q2 2024
    y_minus_2 = current_period - 8 # Q2 2023

    q_minus_1_data = QuarterlyData(ticker=data["ticker"], quarter=f"Q{q_minus_1.quarter}", year=q_minus_1.year)
    q_minus_2_data = QuarterlyData(ticker=data["ticker"], quarter=f"Q{q_minus_2.quarter}", year=q_minus_2.year)
    y_minus_1_data = QuarterlyData(ticker=data["ticker"], quarter=f"Q{y_minus_1.quarter}", year=y_minus_1.year)
    y_minus_2_data = QuarterlyData(ticker=data["ticker"], quarter=f"Q{y_minus_2.quarter}", year=y_minus_2.year)

    q_minus_1_doc = eight_k_collection.find_one({
        "ticker": data["ticker"],
        "quarter": f"Q{q_minus_1.quarter}",
        "year": q_minus_1.year,
    })

    q_minus_2_doc = eight_k_collection.find_one({
        "ticker": data["ticker"],
        "quarter": f"Q{q_minus_2.quarter}",
        "year": q_minus_2.year
    })

    y_minus_1_doc = eight_k_collection.find_one({
        "ticker": data["ticker"],
        "quarter": f"Q{y_minus_1.quarter}",
        "year": y_minus_1.year
    })

    y_minus_2_doc = eight_k_collection.find_one({
        "ticker": data["ticker"],
        "quarter": f"Q{y_minus_2.quarter}",
        "year": y_minus_2.year
    })

    if q_minus_2_doc:
        q_minus_2_data = QuarterlyData(
            ticker=q_minus_2_doc["ticker"],
            quarter=q_minus_2_doc["quarter"],
            year=q_minus_2_doc["year"],
            currency=q_minus_2_doc.get("currency"),
            revenue=q_minus_2_doc.get("revenue"),
            gaap_gross_margin=q_minus_2_doc.get("gaap_gross_margin"),
            non_gaap_gross_margin=q_minus_2_doc.get("non_gaap_gross_margin"),
            gaap_operating_margin=q_minus_2_doc.get("gaap_operating_margin"),
            non_gaap_operating_margin=q_minus_2_doc.get("non_gaap_operating_margin")
        )

    if q_minus_1_doc:
        q_minus_1_data = QuarterlyData(
            ticker=q_minus_1_doc["ticker"],
            quarter=q_minus_1_doc["quarter"],
            year=q_minus_1_doc["year"],
            currency=q_minus_1_doc["currency"],
            revenue=q_minus_1_doc["revenue"],
            revenue_qoq_growth=calculate_growth(q_minus_2_data.revenue, q_minus_1_doc["revenue"]),
            gaap_gross_margin=q_minus_1_doc["gaap_gross_margin"],
            gaap_gross_margin_qoq_change=calculate_bps_change(q_minus_2_data.gaap_gross_margin, q_minus_1_doc["gaap_gross_margin"]),
            non_gaap_gross_margin=q_minus_1_doc["non_gaap_gross_margin"],
            non_gaap_gross_margin_qoq_change=calculate_bps_change(q_minus_2_data.non_gaap_gross_margin, q_minus_1_doc["non_gaap_gross_margin"]),
            gaap_operating_margin=q_minus_1_doc["gaap_operating_margin"],
            gaap_operating_margin_qoq_change=calculate_bps_change(q_minus_2_data.gaap_operating_margin, q_minus_1_doc["gaap_operating_margin"]),
            non_gaap_operating_margin=q_minus_1_doc["non_gaap_operating_margin"],
            non_gaap_operating_margin_qoq_change=calculate_bps_change(q_minus_2_data.non_gaap_operating_margin, q_minus_1_doc["non_gaap_operating_margin"]),
        )
    
    if y_minus_2_doc:
        y_minus_2_data = QuarterlyData(
            ticker=y_minus_2_doc["ticker"],
            quarter=y_minus_2_doc["quarter"],
            year=y_minus_2_doc["year"],
            currency=y_minus_2_doc.get("currency"),
            revenue=y_minus_2_doc.get("revenue"),
            gaap_gross_margin=y_minus_2_doc.get("gaap_gross_margin"),
            non_gaap_gross_margin=y_minus_2_doc.get("non_gaap_gross_margin"),
            gaap_operating_margin=y_minus_2_doc.get("gaap_operating_margin"),
            non_gaap_operating_margin=y_minus_2_doc.get("non_gaap_operating_margin")
        )

    if y_minus_1_doc:
        y_minus_1_data = QuarterlyData(
            ticker=y_minus_1_doc["ticker"],
            quarter=y_minus_1_doc["quarter"],
            year=y_minus_1_doc["year"],
            currency=y_minus_1_doc["currency"],
            revenue=y_minus_1_doc["revenue"],
            revenue_yoy_growth=calculate_growth(y_minus_2_data.revenue, y_minus_1_doc["revenue"]),
            gaap_gross_margin=y_minus_1_doc["gaap_gross_margin"],
            gaap_gross_margin_yoy_change=calculate_bps_change(y_minus_2_data.gaap_gross_margin, y_minus_1_doc["gaap_gross_margin"]),
            non_gaap_gross_margin=y_minus_1_doc["non_gaap_gross_margin"],
            non_gaap_gross_margin_yoy_change=calculate_bps_change(y_minus_2_data.non_gaap_gross_margin, y_minus_1_doc["non_gaap_gross_margin"]),
            gaap_operating_margin=y_minus_1_doc["gaap_operating_margin"],
            gaap_operating_margin_yoy_change=calculate_bps_change(y_minus_2_data.gaap_operating_margin, y_minus_1_doc["gaap_operating_margin"]),
            non_gaap_operating_margin=y_minus_1_doc["non_gaap_operating_margin"],
            non_gaap_operating_margin_yoy_change=calculate_bps_change(y_minus_2_data.non_gaap_operating_margin, y_minus_1_doc["non_gaap_operating_margin"]),
        )

    current_data = QuarterlyData(
        ticker=data["ticker"],
        quarter=data["quarter"],
        year=data["year"],
        currency=data.get("currency"),
        revenue=data.get("revenue"),
        revenue_qoq_growth=calculate_growth(q_minus_1_data.revenue, data.get("revenue")),
        revenue_yoy_growth=calculate_growth(y_minus_1_data.revenue, data.get("revenue")),
        gaap_gross_margin=data.get("gaap_gross_margin"),
        gaap_gross_margin_qoq_change=calculate_bps_change(q_minus_1_data.gaap_gross_margin, data.get("gaap_gross_margin")),
        gaap_gross_margin_yoy_change=calculate_bps_change(y_minus_1_data.gaap_gross_margin, data.get("gaap_gross_margin")),
        non_gaap_gross_margin=data.get("non_gaap_gross_margin"),
        non_gaap_gross_margin_qoq_change=calculate_bps_change(q_minus_1_data.non_gaap_gross_margin, data.get("non_gaap_gross_margin")),
        non_gaap_gross_margin_yoy_change=calculate_bps_change(y_minus_1_data.non_gaap_gross_margin, data.get("non_gaap_gross_margin")),
        gaap_operating_margin=data.get("gaap_operating_margin"),
        gaap_operating_margin_qoq_change=calculate_bps_change(q_minus_1_data.gaap_operating_margin, data.get("gaap_operating_margin")),
        gaap_operating_margin_yoy_change=calculate_bps_change(y_minus_1_data.gaap_operating_margin, data.get("gaap_operating_margin")),
        non_gaap_operating_margin=data.get("non_gaap_operating_margin"),
        non_gaap_operating_margin_qoq_change=calculate_bps_change(q_minus_1_data.non_gaap_operating_margin, data.get("non_gaap_operating_margin")),
        non_gaap_operating_margin_yoy_change=calculate_bps_change(y_minus_1_data.non_gaap_operating_margin, data.get("non_gaap_operating_margin"))
    )
        
    return current_data, q_minus_1_data, y_minus_1_data