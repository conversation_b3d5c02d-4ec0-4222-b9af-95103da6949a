from src.core.logging import get_logger, configure_logging
from src.core.constants import LOG_LEVEL
from src.database.factory import DatabaseFactory
from src.services.sec_api import search_8k_filings
from src.earnings_message.lookback import lookback_data
from datetime import datetime, timedelta
import pandas as pd
from src.earnings_message.processing import process_one_filing
from src.earnings_message.calculations import calculate_financial_data
from src.earnings_message.generate_analysis import (
    generate_revenue_analysis,
    generate_gross_margin_analysis,
    generate_operating_margin_analysis
)

logger = get_logger(__name__)
configure_logging(log_level=LOG_LEVEL)

connection = DatabaseFactory.get_mongo_connection()
eight_k_collection = connection.get_filings_collection("eight_k_filings")
earning_analysis_collection = connection.get_collection("earnings_analysis")
quant_collection = connection.get_stock_collection("quant_data")

def process_one_ticker(ticker: str):
    # eight_k_filings = search_8k_filings(ticker=ticker, start_date=datetime(2025,7,23), end_date=datetime(2025,7,24))
    eight_k_filings = [{}]
    if eight_k_filings:
        logger.info(f"Found {len(eight_k_filings)} 8-K filings for {ticker}")
        for filing in eight_k_filings:
            # upsert_doc = process_one_filing(ticker, filing)
            # if not upsert_doc:
            #     logger.warning(f"Could not process filing for {ticker}: {filing}")
            #     continue
            # # Update to avoid duplicates
            # eight_k_collection.update_one(
            #     {"quarter": upsert_doc["quarter"], "year": upsert_doc["year"]},
            #     {"$set": upsert_doc},
            #     upsert=True
            # )
            # fetch doc for testing
            upsert_doc = eight_k_collection.find_one({"ticker": ticker, "quarter": "Q2", "year": 2025})

            current_data, previous_quarter_data, previous_year_data = lookback_data(upsert_doc)

            revenue_figures, gross_margin_figures, operating_margin_figures = calculate_financial_data(
                current_data, previous_quarter_data, previous_year_data
            )

            period_obj = pd.Period(f"{upsert_doc['year']}{upsert_doc['quarter']}")
            current_period = f"Q{period_obj.quarter}-{period_obj.year}"
            previous_year = f"Q{(period_obj - 4).quarter}-{(period_obj - 4).year}"
            previous_quarter = f"Q{(period_obj - 1).quarter}-{(period_obj - 1).year}"
            
            full_message = ""
            revenue_analysis = generate_revenue_analysis(current_period, previous_quarter, previous_year, revenue_figures) 
            if revenue_analysis: full_message += "*Revenue*: " +revenue_analysis + " "
            gross_margin_analysis = generate_gross_margin_analysis(current_period, previous_quarter, previous_year, gross_margin_figures)
            if gross_margin_analysis: full_message += "*Gross Margin*: " +gross_margin_analysis + " "
            operating_margin_analysis = generate_operating_margin_analysis(current_period, previous_quarter, previous_year, operating_margin_figures)
            if operating_margin_analysis: full_message += "*Operating Margin*: " +operating_margin_analysis + " "

            print(full_message)