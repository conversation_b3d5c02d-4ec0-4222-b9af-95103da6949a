from typing import TypedDict, Optional
from typing_extensions import NotRequired
from datetime import datetime
from bson import ObjectId
from pydantic import BaseModel
from dataclasses import dataclass

@dataclass
class QuarterlyData:
    ticker: str
    quarter: str
    year: int
    currency: Optional[str] = None

    revenue: Optional[float] = None
    revenue_qoq_growth: Optional[float] = None
    revenue_yoy_growth: Optional[float] = None

    gaap_gross_margin: Optional[float] = None
    gaap_gross_margin_qoq_change: Optional[float] = None
    gaap_gross_margin_yoy_change: Optional[float] = None

    non_gaap_gross_margin: Optional[float] = None
    non_gaap_gross_margin_qoq_change: Optional[float] = None
    non_gaap_gross_margin_yoy_change: Optional[float] = None

    gaap_operating_margin: Optional[float] = None
    gaap_operating_margin_qoq_change: Optional[float] = None
    gaap_operating_margin_yoy_change: Optional[float] = None

    non_gaap_operating_margin: Optional[float] = None
    non_gaap_operating_margin_qoq_change: Optional[float] = None
    non_gaap_operating_margin_yoy_change: Optional[float] = None

    revenue_growth_guidance: Optional[float] = None
    gaap_gross_margin_guidance: Optional[float] = None
    non_gaap_gross_margin_guidance: Optional[float] = None
    gaap_operating_margin_guidance: Optional[float] = None
    non_gaap_operating_margin_guidance: Optional[float] = None

class EightKFilingsData(TypedDict):
    """The structure to store data in mongodb 8-K filings collection"""
    _id: NotRequired[ObjectId]
    event_id: Optional[ObjectId]
    ticker: str
    quarter: str
    year: int
    currency: str
    revenue: Optional[float]
    gaap_gross_margin: Optional[float]
    non_gaap_gross_margin: Optional[float]
    gaap_operating_margin: Optional[float]
    non_gaap_operating_margin: Optional[float]
    revenue_growth_guidance: Optional[float]
    gaap_gross_margin_guidance: Optional[float]
    non_gaap_gross_margin_guidance: Optional[float]
    gaap_operating_margin_guidance: Optional[float]
    non_gaap_operating_margin_guidance: Optional[float]
    filed_at: datetime
    cik: str
    accession_number: str
    filing_type: str
    document_url: str
    updated_at: datetime


class ExtractionResponse(BaseModel):
    """Validations used to parse structured output from LLM"""
    quarter: Optional[str]
    year: Optional[int]
    currency: Optional[str]
    reported_quarter_revenue: Optional[float]
    reported_quarter_gaap_gross_margin: Optional[float]
    reported_quarter_non_gaap_gross_margin: Optional[float]
    reported_quarter_gaap_operating_margin: Optional[float]
    reported_quarter_non_gaap_operating_margin: Optional[float]
    next_quarter_revenue_growth_guidance: Optional[float]
    next_quarter_gross_margin_guidance: Optional[float]
    next_quarter_operating_margin_guidance: Optional[float]

class RevenueFigures(TypedDict):
    reported_quarter_revenue: Optional[float]
    current_yoy_growth: Optional[float]
    previous_yoy_growth: Optional[float]
    current_qoq_growth: Optional[float]
    previous_qoq_growth: Optional[float]

class GrossMarginFigures(TypedDict):
    reported_quarter_gross_margin: Optional[float]
    yoy_margin_change: Optional[float]
    qoq_margin_change: Optional[float]

class OperatingMarginFigures(TypedDict):
    reported_quarter_operating_margin: Optional[float]
    yoy_margin_change: Optional[float]
    qoq_margin_change: Optional[float]

class GuidanceFigures(TypedDict):
    revenue_growth_guidance: Optional[float]
    current_qoq_growth_guidance: Optional[float]
    previous_qoq_growth_guidance: Optional[float]
    gross_margin_guidance: Optional[float]
    operating_margin_guidance: Optional[float]

class CRPOBillingsFigures(TypedDict):
    current_crpo: Optional[float]
    current_billings: Optional[float]
    crpo_qoq_growth: Optional[float]
    billings_qoq_growth: Optional[float]
    previous_crpo_qoq_growth: Optional[float]
    previous_billings_qoq_growth: Optional[float]