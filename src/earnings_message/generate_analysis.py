from src.earnings_message.schemas import (
    RevenueFigures,
    GrossMarginFigures,
    OperatingMarginFigures,
    GuidanceFigures,
)

def format_change(value: float, is_percent: bool = True) -> str:
    """Format change values with appropriate verb (increased/decreased)."""
    verb = "increased" if value >= 0 else "decreased"
    unit = "%" if is_percent else " basis points (bps)"
    return f"{verb} {format_number(abs(value))}{unit}"


def format_change_value(value: float, is_percent: bool = True) -> str:
    """Format just the change value with sign."""
    sign = "+" if value >= 0 else "-"
    unit = "%" if is_percent else " bps"
    return f"{sign}{format_number(abs(value), decimals=0)}{unit}"


def format_number(value: float, decimals: int = 2) -> str:
    """Smart number formatting for financial reports."""
    if abs(value) < 0.1 and value != 0:
        abs_val = abs(value)
        if abs_val >= 0.01:
            return f"{value:.2f}"
        elif abs_val >= 0.001:
            return f"{value:.3f}"
        elif abs_val >= 0.0001:
            return f"{value:.4f}"
        else:
            return f"{value:.{decimals}f}"  # fallback
    return f"{value:.1f}"


def generate_revenue_analysis(
    current_period: str, previous_quarter: str, previous_year: str, data: RevenueFigures
) -> str | None:
    if not data["current_yoy_growth"]:
        return None

    components = []
    
    # YoY statement
    yoy_parts = [f"In {current_period}, revenue {format_change(data['current_yoy_growth'])} year-over-year (YoY)"]
    if data["previous_yoy_growth"] is not None:
        yoy_parts.append(f"compared to {format_number(abs(data['previous_yoy_growth']))}% growth in {previous_year}")
    components.append(", ".join(yoy_parts))
    
    # QoQ statement
    if data["current_qoq_growth"] is not None:
        qoq_parts = [f"Quarter-over-quarter (QoQ), revenue grew {format_number(abs(data['current_qoq_growth']))}%"]
        if data["previous_qoq_growth"] is not None:
            qoq_parts.append(f"versus {format_number(abs(data['previous_qoq_growth']))}% in {previous_quarter}")
        components.append(", ".join(qoq_parts))
    
    return ". ".join(components) + "."


def generate_gross_margin_analysis(
    current_period: str, previous_quarter: str, previous_year: str, data: GrossMarginFigures
) -> str | None:
    if not data["reported_quarter_gross_margin"]:
        return None

    parts = [f"Gross margins for {current_period} were {format_number(data['reported_quarter_gross_margin'])}%"]
    
    if data["yoy_margin_change"] is not None or data["qoq_margin_change"] is not None:
        parts.append("representing")
        
        changes = []
        if data["yoy_margin_change"] is not None:
            changes.append(f"a {format_change_value(data['yoy_margin_change'], False)} change from {previous_year}")
        
        if data["qoq_margin_change"] is not None:
            conjunction = "and" if changes else ""
            changes.append(f"{conjunction} a {format_change_value(data['qoq_margin_change'], False)} change from {previous_quarter}")
        
        parts.append(" ".join(changes).strip())
    
    return " ".join(parts) + "."


def generate_operating_margin_analysis(
    current_period: str, previous_quarter: str, previous_year: str, data: OperatingMarginFigures
) -> str | None:
    if not data["reported_quarter_operating_margin"]:
        return None

    parts = [f"Operating margins stood at {format_number(data['reported_quarter_operating_margin'])}% in {current_period}"]
    
    changes = []
    if data["yoy_margin_change"] is not None:
        changes.append(f"a {format_change_value(data['yoy_margin_change'], False)} change compared to {previous_year}")
    
    if data["qoq_margin_change"] is not None:
        conjunction = "and" if changes else "a"
        changes.append(f"{conjunction} {format_change_value(data['qoq_margin_change'], False)} change versus {previous_quarter}")
    
    if changes:
        parts.append(", ".join(changes))
    
    return ", ".join(parts) + "."


def generate_guidance_analysis(
        current_period: str, previous_quarter: str, next_quarter: str, data: GuidanceFigures
) -> str|None:
    message = []
    if data["revenue_growth_guidance"] and data["operating_margin_guidance"] and data["gross_margin_guidance"]:
        message = f"Guidance: For {next_quarter}, revenue is expected to grow {data['revenue_growth_guidance']}% YoY, "\
                f"implying a {data['current_qoq_growth_guidance']}% deceleration from {current_period}’s YoY growth. "\
                f"This compares to a {data['previous_qoq_growth_guidance']}% deceleration guided when {previous_quarter} results were reported. "\
                f"Gross margins for {next_quarter} are guided at {data['gross_margin_guidance']}%, a _ bps change from {current_period}. "
                f"Operating margins for {next_quarter} are guided at {data['operating_margin_guidance']}%, a _ bps change from {current_period}."
