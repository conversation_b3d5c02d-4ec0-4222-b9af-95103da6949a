from src.earnings_message.schemas import EightKFilingsData
from typing import Optional
from src.earnings_message.text_reader import extract_text_from_url
from src.earnings_message.structured_output import extract_information
from src.database.factory import DatabaseFactory
from datetime import datetime

connection = DatabaseFactory.get_mongo_connection()
earning_analysis_collection = connection.get_collection("earnings_analysis")
quant_collection = connection.get_stock_collection("quant_data")

def process_one_filing(ticker: str, filing: dict) -> Optional[EightKFilingsData]:
    if not filing.get("filedAt") or not filing.get("cik") or not filing.get("accessionNo"):
        print(f"Missing required fields in filing {filing.get('accessionNo')}")
        return None

    # Extract EX-99.1 document URL efficiently
    ex_99_1_url = None
    document_format_files = filing.get("documentFormatFiles", [])
    for doc in document_format_files:
        if doc.get("type") == "EX-99.1":
            ex_99_1_url = doc.get("documentUrl")
            break  # Stop once we find the first EX-99.1
    
    if not ex_99_1_url:
        print(f"No EX-99.1 document found for filing {filing.get('accessionNo')}")
        return None

    filings_text = extract_text_from_url(ex_99_1_url)
    filings_data = extract_information(filings_text)

    if not filings_data.quarter or not filings_data.year or not filings_data.currency:
        print(f"Missing required fields in extracted data for filing {filing.get('accessionNo')}")
        return None

    if not filings_data.reported_quarter_revenue:
        quant = quant_collection.find_one({"ticker": ticker, "fiscalperiod": f"{filings_data.year}-{filings_data.quarter}"})
        if quant:
            filings_data.reported_quarter_revenue = quant.get("revenue")

    event = earning_analysis_collection.find_one(
        {"ticker": ticker, "quarter": filings_data.quarter, "year": filings_data.year}
    )

    # Process and store each filing
    return EightKFilingsData(
        event_id = event.get("event_id") if event else None,
        ticker = ticker,
        quarter = filings_data.quarter,
        year = filings_data.year,
        currency = filings_data.currency,
        revenue = filings_data.reported_quarter_revenue,
        gaap_gross_margin = filings_data.reported_quarter_gaap_gross_margin,
        non_gaap_gross_margin = filings_data.reported_quarter_non_gaap_gross_margin,
        gaap_operating_margin = filings_data.reported_quarter_gaap_operating_margin,
        non_gaap_operating_margin = filings_data.reported_quarter_non_gaap_operating_margin,
        revenue_growth_guidance = filings_data.next_quarter_revenue_growth_guidance,
        gross_margin_guidance = filings_data.next_quarter_gross_margin_guidance,
        operating_margin_guidance = filings_data.next_quarter_operating_margin_guidance,
        filed_at = filing["filedAt"],
        cik = filing["cik"],
        accession_number = filing["accessionNo"],
        filing_type = "EX-99.1",
        document_url = ex_99_1_url,
        updated_at = datetime.now()
    )