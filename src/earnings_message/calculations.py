from src.earnings_message.schemas import RevenueFigures, GrossMarginFigures, OperatingMarginFigures
from src.earnings_message.schemas import QuarterlyData
from typing import Optional, <PERSON><PERSON>


def calculate_financial_data(
    current_data: QuarterlyData,
    previous_quarter_data: QuarterlyData,
    previous_year_data: QuarterlyData,
) -> Tuple[RevenueFigures, GrossMarginFigures, OperatingMarginFigures]:
    
    revenue = RevenueFigures(
        reported_quarter_revenue=current_data.revenue,
        current_yoy_growth=current_data.revenue_yoy_growth,
        previous_yoy_growth=previous_year_data.revenue_yoy_growth,
        current_qoq_growth=current_data.revenue_qoq_growth,
        previous_qoq_growth=previous_quarter_data.revenue_qoq_growth,
    )

    # If Non-GAAP gross margin is available use that across the board, otherwise use GAAP gross margin
    if current_data.non_gaap_gross_margin is not None:
        gross_margin = current_data.non_gaap_gross_margin
        gross_margin_qoq_change = current_data.non_gaap_gross_margin_qoq_change
        gross_margin_yoy_change = current_data.non_gaap_gross_margin_yoy_change
    else:
        gross_margin = current_data.gaap_gross_margin
        gross_margin_qoq_change = current_data.gaap_gross_margin_qoq_change
        gross_margin_yoy_change = current_data.gaap_gross_margin_yoy_change


    gross_margin = GrossMarginFigures(
        reported_quarter_gross_margin=gross_margin,
        yoy_margin_change=gross_margin_yoy_change,
        qoq_margin_change=gross_margin_qoq_change
    )

    # If Non-GAAP operating margin is available use that across the board, otherwise use GAAP operating margin
    if current_data.non_gaap_operating_margin is not None:
        operating_margin = current_data.non_gaap_operating_margin
        operating_margin_qoq_change = current_data.non_gaap_operating_margin_qoq_change
        operating_margin_yoy_change = current_data.non_gaap_operating_margin_yoy_change
    else:
        operating_margin = current_data.gaap_operating_margin
        operating_margin_qoq_change = current_data.gaap_operating_margin_qoq_change
        operating_margin_yoy_change = current_data.gaap_operating_margin_yoy_change

    operating_margin = OperatingMarginFigures(
        reported_quarter_operating_margin=operating_margin,
        yoy_margin_change=operating_margin_yoy_change,
        qoq_margin_change= operating_margin_qoq_change
    )

    return revenue, gross_margin, operating_margin