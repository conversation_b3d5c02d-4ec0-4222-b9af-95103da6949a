from src.database.factory import DatabaseFactory
from src.services.sec_api import search_8k_filings
from datetime import datetime, timedelta
from src.earnings_message.processing import process_one_filing

connection = DatabaseFactory.get_mongo_connection()
eight_k_collection = connection.get_filings_collection("eight_k_filings")

def populate_historical_data(ticker: str) -> None:
    start_date = datetime(2019, 1, 1)
    end_date = datetime.now()
    current_date = start_date
    
    while current_date <= end_date:
        # Calculate the end of the 30-day period
        period_end = min(current_date + timedelta(days=29), end_date)
        
        print(f"Processing period: {current_date.strftime('%Y-%m-%d')} to {period_end.strftime('%Y-%m-%d')}")
        
        # Get all filings for this 30-day period using pagination
        filings = search_8k_filings(ticker, current_date, period_end)
        
        if filings:
            print(f"  Found {len(filings)} filings")
            for filing in filings:
                upsert_doc = process_one_filing(ticker, filing)
                # Insert into MongoDB only if EX-99.1 exists
                if upsert_doc:
                    eight_k_collection.insert_one(upsert_doc)
        
        # Move to the next 30-day period
        current_date = period_end + timedelta(days=1)