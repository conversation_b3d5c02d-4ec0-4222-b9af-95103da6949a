import re
from sec_api import RenderApi
from src.core.constants import SEC_API_KEY
from html_to_markdown import convert_to_markdown

renderApi = RenderApi(api_key=SEC_API_KEY)

def extract_text_from_url(url: str) -> str|None:
    html_content = renderApi.get_file(url)
    if html_content and isinstance(html_content, str):
        markdown = convert_to_markdown(html_content)
        return markdown
    return None