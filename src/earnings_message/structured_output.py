from src.services.llm.openai_service import OpenAIService
from src.earnings_message.schemas import ExtractionResponse
from src.earnings_message.prompt import PROMPT_TEMPLATE
import re
import pandas as pd
from pydantic import ValidationError

openai = OpenAIService()

def extract_information(text: str) -> ExtractionResponse:
    raw_response = openai.get_completion(
        prompt=PROMPT_TEMPLATE.format(filings_text=text)
    )

    # Try to find JSO<PERSON> inside triple backticks first
    match = re.search(r"```(?:json)?\s*(.*?)\s*```", raw_response, re.DOTALL | re.IGNORECASE)
    if match:
        json_str = match.group(1)
    else:
        # Fallback: extract first JSON object in the text
        match = re.search(r"\{[\s\S]*\}", raw_response)
        if match:
            json_str = match.group(0)
        else:
            raise ValueError(f"No JSON found in model response: {raw_response[:200]}...")

    try:
        out = ExtractionResponse.model_validate_json(json_str)
    except ValidationError as e:
        raise ValueError(f"Invalid JSON for FinancialData: {e}\nExtracted: {json_str}")

    return out