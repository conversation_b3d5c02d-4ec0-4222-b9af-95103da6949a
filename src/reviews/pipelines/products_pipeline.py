from src.reviews.core.preprocessing import preprocess_reviews
from src.reviews.core.categorization import categorize_reviews
from src.reviews.storage.chunks import products_store_review_chunks
from src.reviews.storage.insights import products_update_insights_collection
from src.reviews.core.filter import filter_reviews
from src.reviews.schemas import ProductReviewCategory
from src.core.logging import get_logger, configure_logging
from datetime import datetime, timedelta
from pymongo.collection import Collection
from src.database.factory import DatabaseFactory

logger = get_logger(__name__)
configure_logging()

connection = DatabaseFactory().get_mongo_connection()

ticker_collection: Collection = connection.get_blackberry_employee_collection("trustpilot_companies")
reviews_collection: Collection = connection.get_blackberry_product_collection("trustpilot_reviews")

insights_collection: Collection = connection.get_product_insight_collection("tp_insights")
review_chunks_collection: Collection = connection.get_product_insight_collection("review_chunks")
summary_collection: Collection = connection.get_product_insight_collection("tp_summary")
prompts_collection: Collection = connection.get_product_insight_collection("prompts")
categories_collection: Collection = connection.get_product_insight_collection("categories")

def process_one_ticker(ticker: str, CUTOFF_DATE: datetime, END_DATE: datetime) -> None:
    logger.info(f"Processing ticker: {ticker}")
    query = {
        "ticker": ticker,
        "cutoff_date": {
            "$gte": CUTOFF_DATE,
            "$lt": CUTOFF_DATE + timedelta(days=1)
        }
    }
    if insights_collection.find_one(query):
        logger.info(f"Ticker {ticker} already exists in insights collection. Skipping...")
        return
    logger.info(f"Processing ticker {ticker}")

    review_articles = filter_reviews("ticker", ticker, "date", "datetime", CUTOFF_DATE, END_DATE, None, reviews_collection)
    if len(review_articles) == 0:
        logger.info(f"No reviews found for ticker: {ticker}")
        return
    review_articles_string = preprocess_reviews(review_articles, field_name="content", id_prefix="")

    prompt_doc = prompts_collection.find_one({"prompt_name": "products_reviews_categorization_prompt", "version": "1.0"})
    if not prompt_doc:
        logger.error("Prompt not found")
        return

    # The llm is classifying reviews for each product
    review_list = categorize_reviews(prompt_doc["prompt"], ProductReviewCategory, ticker=ticker, reviews_articles_str=review_articles_string)

    for review in review_list:
        products_store_review_chunks(ticker, review, reviews_collection, review_chunks_collection, CUTOFF_DATE, END_DATE)

    products_for_ticker = review_chunks_collection.distinct("product", {"ticker": ticker})
    for product in products_for_ticker:
        review_count = review_chunks_collection.count_documents({"ticker": ticker, "product": product})
        if review_count == 0:
            logger.info(f"No reviews found for product {product}. Skipping...")
            continue
        logger.info(f"Number of reviews for product '{product}': {review_count}")
        reviews = review_chunks_collection.find({"ticker": ticker, "product": product, "cutoff_date": CUTOFF_DATE})
        # review_text = ""
        # for review in reviews:
        #     review_text += f"[review_id: {review['review_id']}] {review['review_text']}\n"
        review_text = " ".join([f"[review_id: {review['review_id']}] {review['review_text']}" for review in reviews])   # Check if review_text is empty
        if not review_text:
            logger.info(f"No review text found for product: {product}")
            continue
        products_update_insights_collection(ticker, product, review_count, review_text, insights_collection, CUTOFF_DATE, END_DATE)
