from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def get_insight_refinement_prompt(trend, trend_rationale):
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    insight_refinement_prompt = prompt_collection.find_one({"prompt_name": "insight_refinement_prompt"})["prompt"]
    prompt = insight_refinement_prompt.format(trend=trend, trend_rationale=trend_rationale)
    return prompt


def create_insights_mda_collection(cutoff_date, lookback_days, batch_size=100):
    """
    Loop through each question in LLM_trend and ask OpenAI to generate insights.
    Uses batch processing to avoid cursor timeouts during long-running LLM calls.
    """
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    llm_mda_trend_collection = connection.get_collection("LLM_MDA_trend")
    llm_mda_insights_collection = connection.get_collection("LLM_MDA_insights")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "select_for_llm_insights_mda_processing": 1
    }

    # Process documents in batches
    processed_count = 0
    deleted_events = []
    with llm_mda_trend_collection.find(query, no_cursor_timeout=True).batch_size(100) as llm_mda_trends:
        # Process each document in the batch
        for llm_mda_trend in llm_mda_trends:
            qnaId = llm_mda_trend["qnaId"]
            event_id = llm_mda_trend["event_id"]
            event_name = llm_mda_trend["event"]
            date = llm_mda_trend["date"]
            mda_chunk = llm_mda_trend["answer"]
            ticker = llm_mda_trend["ticker"]
            mda_chunk_category = llm_mda_trend["category"]
            mda_chunk_importance = llm_mda_trend["importance"]
            if event_id not in deleted_events:
                delete_records_marked_for_deletion(event_id=event_id, coll=llm_mda_insights_collection)
                deleted_events.append(event_id)

            trend = llm_mda_trend["trend"]
            trend_rationale = llm_mda_trend["trend_rationale"]

            # Check if insight already exists
            if llm_mda_insights_collection.find_one({"mda_chunk_id": llm_mda_trend["mda_chunk_id"]}):
                logger.info("LLM insight already exists")
                # Mark as processed and continue
                llm_mda_trend_collection.update_one(
                    {"_id": llm_mda_trend["_id"]},
                    {"$set": {"select_for_llm_insights_mda_processing": 0}}
                )
                processed_count += 1
                continue

            if date < cutoff_date_start or date > cutoff_date_end:
                # Mark as processed and continue
                llm_mda_trend_collection.update_one(
                    {"_id": llm_mda_trend["_id"]},
                    {"$set": {"select_for_llm_insights_mda_processing": 0}}
                )
                processed_count += 1
                continue

            # Create the prompt
            insight_refinement_prompt = get_insight_refinement_prompt(trend=trend, trend_rationale=trend_rationale)

            # Ask OpenAI to generate insight
            insight = openai_service.get_completion(insight_refinement_prompt)
            logger.info(insight)

            # Store the insight in MongoDB
            document = {
                "mda_chunk_id": llm_mda_trend["mda_chunk_id"],
                "chunk_id": llm_mda_trend["chunk_id"],
                "qnaId": qnaId,
                "ticker": ticker,
                "event_id": event_id,
                "event": event_name,
                "answer": mda_chunk,
                "rationale": trend_rationale,
                "trend": trend,
                "insight": insight,
                "date": date,
                "category": mda_chunk_category,
                "importance": mda_chunk_importance,
                "updated_at": datetime.now()
            }

            # Update the trend document and insert the insight document
            llm_mda_trend_collection.update_one(
                {"_id": llm_mda_trend["_id"]},
                {"$set": {"select_for_llm_insights_mda_processing": 0}}
            )
            llm_mda_insights_collection.insert_one(document)
            processed_count += 1

        logger.info(f"Completed batch, {processed_count} documents processed so far")

    return
