import numpy as np
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.core.logging import get_logger

logger = get_logger(__name__)


def calculate_sentiment(net, down, down_th, net_th):
    if down < down_th:
        sentiment = "negative"
    elif net > net_th:
        sentiment = "positive"
    else:
        sentiment = "neutral"
    return sentiment


def _calculate_event_score(calculated_sentiment, net_score, downtick_score, mean_downtick_score, std_net_score, std_downtick_score, mean_net):

    if std_downtick_score != 0:
        downtick_normal = (downtick_score - mean_downtick_score) / std_downtick_score
    else:
        downtick_normal = -1

    if std_net_score != 0:
        net_normal = ((net_score - mean_net)) / (std_net_score)
    else:
        net_normal = 1
    epsilon = 0.05  # Low epsilon to reduce clipping at -0.4 and +0.4
    upper_bound = 0.32  # This results in range of score being <0.4 when neutral
    lower_bound = -0.24  # This results in range of score being <-0.4 when neutral

    if calculated_sentiment == "negative":
        final_score = -np.log(1 - downtick_normal) / np.log(2)
    elif calculated_sentiment == "positive":
        final_score = np.log(1 + net_normal) / np.log(2)
    else:
        final_score = np.log(1 + min(upper_bound, max(lower_bound, epsilon * net_normal))) / np.log(2)

    return final_score


def calculate_mean_sd(sector, date_start, date_end):
    """
    Returns
    -------
    mean_uptick_score, std_dev_uptick_score,
    mean_downtick_score, std_dev_downtick_score,
    uptick_threshold, downtick_threshold, downtick_threshold2, uptick_threshold2
    (unchanged order / count)
    """
    connection = DatabaseFactory().get_mongo_connection()
    coll = connection.get_collection("public_investor_events_outputs")

    def safe(stat_list, field, fallback):
        """Return stat_list[0][field] unless it’s missing or None."""
        return stat_list[0].get(field) if stat_list and stat_list[0].get(field) is not None else fallback

    # ---------- Downtick stats ----------
    downtick_stats = list(coll.aggregate([
        {"$match": {"event_type": "non_earnings", "sector": sector, "date": {"$gte": date_start, "$lte": date_end}}},
        {"$group": {
            "_id": None,
            "mean": {"$avg": "$downtick_score"},
            "sd": {"$stdDevSamp": "$downtick_score"}
        }}
    ]))
    mean_downtick_score = safe(downtick_stats, "mean", -2.5)
    std_dev_downtick_score = safe(downtick_stats, "sd", 4.0)

    # ---------- Difference stats (uptick – downtick) ----------
    net_stats = list(coll.aggregate([
        {"$match": {"event_type": "non_earnings", "sector": sector, "date": {"$gte": date_start, "$lte": date_end}}},
        {"$project": {"sum": {"$add": ["$uptick_score", "$downtick_score"]}}},
        {"$group": {
            "_id": None,
            "mean": {"$avg": "$sum"},
            "sd": {"$stdDevSamp": "$sum"}
        }}
    ]))
    mean_net = safe(net_stats, "mean", 3.8)
    std_dev_net = safe(net_stats, "sd", 5.1)

    # ---------- Thresholds (unchanged logic) ----------
    net_threshold = mean_net + 1 * std_dev_net

    downtick_threshold = mean_downtick_score - 1 * std_dev_downtick_score

    # ---------- Logging ----------
    print(f"Mean diff (up‑down): {mean_net:.4f}")
    print(f"SD diff: {std_dev_net:.4f}")
    print(f"Downtick threshold: {downtick_threshold:.4f}")

    return net_threshold, downtick_threshold, mean_downtick_score, std_dev_net, std_dev_downtick_score, mean_net


def thresholds(sector: str, window_end, analysis_window_days) -> tuple[float, float]:
    """
    Return (net_threshold, downtick_threshold) for the sector,
    using non‑earnings events that ended on window_end (inclusive)
    and started LOOKBACK_DAYS before.
    """
    window_start = window_end - timedelta(days=analysis_window_days)
    net_th, down_th, mean_downtick_score, std_dev_net, std_dev_downtick_score, mean_net = calculate_mean_sd(
        sector,
        datetime.combine(window_start, datetime.min.time()),
        datetime.combine(window_end, datetime.min.time()) - timedelta(seconds=1)
    )
    return net_th, down_th, mean_downtick_score, std_dev_net, std_dev_downtick_score, mean_net


def calculate_event_scores(cutoff_date, lookback_days, anaysis_window_days):
    connection = DatabaseFactory().get_mongo_connection()
    public_investor_events_outputs_collection = connection.get_collection("public_investor_events_outputs")
    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "score": {"$exists": False}
    }

    with public_investor_events_outputs_collection.find(query).sort("date", 1).batch_size(10) as event_outputs:
        for event in event_outputs:
            event_id = event["event_id"]
            uptick_score = event["uptick_score"]
            downtick_score = event["downtick_score"]
            logger.info(f"Started to process scores for event_id: {event_id}")
            sector = event.get("sector", None)
            evt_dt = event["date"].date() if isinstance(event["date"], datetime) else event["date"]
            net_th, down_th, mean_downtick_score, std_dev_net, std_dev_downtick_score, mean_net = thresholds(sector, evt_dt - timedelta(days=1), anaysis_window_days)
            net = uptick_score + downtick_score

            if event.get("output_type") in ["QNA_MDA", "QNA"]:
                evt_dt = event["date"].date() if isinstance(event["date"], datetime) else event["date"]
                net_th, down_th, mean_downtick_score, std_dev_net, std_dev_downtick_score, mean_net = thresholds(sector, evt_dt - timedelta(days=1), anaysis_window_days)
                net = uptick_score + downtick_score
                sentiment = calculate_sentiment(net=net, down=downtick_score, down_th=down_th, net_th=net_th)
                event_score = _calculate_event_score(calculated_sentiment=sentiment, net_score=net, downtick_score=downtick_score, mean_downtick_score=mean_downtick_score, std_net_score=std_dev_net, std_downtick_score=std_dev_downtick_score, mean_net=mean_net)
            else:
                event_score = net_th = down_th = net = mean_downtick_score = std_dev_net = std_dev_downtick_score = mean_net = 0
                sentiment = "no_view"

            document = {
                "score": round(event_score, 2),
                "net_th": round(net_th, 2),
                "down_th": round(down_th, 2),
                "net_score": round(net, 2),
                "downtick_score": downtick_score,
                "mean_downtick_score": round(mean_downtick_score, 2),
                "std_net_score": round(std_dev_net, 2),
                "std_downtick_score": round(std_dev_downtick_score, 2),
                "mean_net": round(mean_net, 2),
            }
            if event["event_type"] != "earnings":
                document["sentiment"] = sentiment

            public_investor_events_outputs_collection.update_one(
                {"event_id": event_id}, {"$set": document},
                upsert=True
            )
            logger.info(f"Completed processing scores for Event Id: {event_id}")


if __name__ == "__main__":
    cutoff_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
    calculate_event_scores(cutoff_date=cutoff_date, lookback_days=500, anaysis_window_days=180)
