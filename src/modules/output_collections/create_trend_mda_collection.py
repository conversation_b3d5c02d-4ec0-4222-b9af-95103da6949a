import json
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def get_mda_trend_prompt(mda_chunk, similar_mda_chunks, classification, rationale, max_words):
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    mda_trend_prompt = prompt_collection.find_one({"prompt_name": "mda_trend_prompt"})["prompt"]
    similar_mda_category_chunk_array = [chunk["chunk"] for chunk in similar_mda_chunks]
    similar_mda_category_chunk_text = '\n'.join(f'- {chunk}' for chunk in similar_mda_category_chunk_array)
    prompt = mda_trend_prompt.format(mda_chunk=mda_chunk, similar_mda_chunks_text=similar_mda_category_chunk_text, classification=classification, rationale=rationale, max_words=max_words)
    return prompt


def create_mda_trend_collection(cutoff_date, lookback_days, max_words, analysis_window_days):
    """
    Loop through each question in LLM_similarity and ask OpenAI to classify the trend of the answer.
    """
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    llm_mda_similarity_collection = connection.get_collection("LLM_MDA_similarity")
    llm_mda_trend_collection = connection.get_collection("LLM_MDA_trend")
    mda_chunk_categories_collection = connection.get_collection("mda_chunk_categories")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end},
        "select_for_llm_trend_mda_processing": 1
    }
    deleted_events = []
    with llm_mda_similarity_collection.find(query, no_cursor_timeout=True).batch_size(200) as llm_mda_similarity_cursor:
        for llm_mda_similarity in llm_mda_similarity_cursor:
            qnaId = llm_mda_similarity["qnaId"]
            date = llm_mda_similarity["date"]
            event_id = llm_mda_similarity["event_id"]
            event_name = llm_mda_similarity["event"]
            mda_chunk = llm_mda_similarity["answer"]
            mda_chunk_category = llm_mda_similarity["category"]
            mda_chunk_importance = llm_mda_similarity["importance"]
            classification = llm_mda_similarity["classification"]
            rationale = llm_mda_similarity["rationale"]
            ticker = llm_mda_similarity["ticker"]
            date = llm_mda_similarity["date"]
            if event_id not in deleted_events:
                delete_records_marked_for_deletion(event_id=event_id, coll=llm_mda_trend_collection)
                deleted_events.append(event_id)

            if date < cutoff_date_start or date > cutoff_date_end:
                continue

            if llm_mda_trend_collection.find_one({"mda_chunk_id": llm_mda_similarity["mda_chunk_id"]}):
                logger.info("LLM trend already exists")
                continue

            if classification == "Similar":
                llm_mda_similarity_collection.update_one(
                    {"_id": llm_mda_similarity["_id"]},
                    {"$set": {"select_for_llm_trend_mda_processing": 0}}
                )
                continue

            mda_chunk_analysis_end = date - timedelta(days=1)
            mda_chunk_analysis_start = mda_chunk_analysis_end - timedelta(days=analysis_window_days)
            mda_chunk_category_query = {
                "category": mda_chunk_category,
                "ticker": ticker,
                "event_id": {"$ne": event_id},
                "date": {"$gte": mda_chunk_analysis_start, "$lte": mda_chunk_analysis_end}
            }
            similar_mda_chunks = mda_chunk_categories_collection.find(mda_chunk_category_query, no_cursor_timeout=True)
            similar_mda_chunks = list(similar_mda_chunks)
            if not similar_mda_chunks:
                llm_mda_similarity_collection.update_one(
                    {"_id": llm_mda_similarity["_id"]},
                    {"$set": {"select_for_llm_trend_mda_processing": 0}}
                )
                continue
            mda_trend_prompt = get_mda_trend_prompt(mda_chunk=mda_chunk, similar_mda_chunks=similar_mda_chunks, classification=classification, rationale=rationale, max_words=max_words)
            mda_trend = openai_service.get_completion(mda_trend_prompt)
            data = json.loads(mda_trend)
            mda_trend_classification = data["trend"]
            logger.info(mda_trend_classification)
            mda_trend_rationale = data["rationale"]

            document = {
                "mda_chunk_id": llm_mda_similarity["mda_chunk_id"],
                "chunk_id": llm_mda_similarity["chunk_id"],
                "qnaId": qnaId,
                "ticker": ticker,
                "event_id": event_id,
                "event": event_name,
                "answer": mda_chunk,
                "classification": classification,
                "rationale": rationale,
                "trend": mda_trend_classification,
                "trend_rationale": mda_trend_rationale,
                "date": date,
                "category": mda_chunk_category,
                "importance": mda_chunk_importance,
                "select_for_llm_insights_mda_processing": 1,
                "updated_at": datetime.now()
            }
            llm_mda_similarity_collection.update_one(
                {"_id": llm_mda_similarity["_id"]},
                {"$set": {"select_for_llm_trend_mda_processing": 0}}
            )
            llm_mda_trend_collection.insert_one(document)
    return


if __name__ == "__main__":
    create_mda_trend_collection("2025-04-13", 500, 50, 180)
