import json
import pandas as pd
from datetime import datetime, timedelta
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.core.logging import get_logger
from src.database.mongo.queries import delete_records_marked_for_deletion

logger = get_logger(__name__)


def generate_category_prompt(question, answer, classification_list, sector):
    """
    Generate a prompt for classifying a QnA into one of the topics for a specific sector.
    """
    connection = DatabaseFactory().get_mongo_connection()
    prompt_collection = connection.get_collection("prompts")
    classification_list_text = '\n'.join(f'- {category}' for category in classification_list)
    categorize_management_answers_prompt = prompt_collection.find_one({"prompt_name": "categorize_management_answers_prompt"})["prompt"]
    prompt = categorize_management_answers_prompt.format(classification_list_text=classification_list_text, question=question, answer=answer, sector=sector)
    return prompt


def create_category_collection(cutoff_date, lookback_days):
    """
    Loop through each QnA, generate a prompt, call the LLM to classify, and store the results in MongoDB.
    """
    # Get list of qnas from qnas collection
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    qna_collection = connection.get_collection("qnas")
    companies_collection = connection.get_collection("companies")
    categories_collection = connection.get_collection("sector_categories")
    qna_importance_collection = connection.get_collection("qna_importance")
    # llm_trend_collection = connection.get_collection("LLM_trend")

    cutoff_date_end = datetime.strptime(cutoff_date, "%Y-%m-%d")
    cutoff_date_start = cutoff_date_end - timedelta(days=lookback_days)

    tickers = companies_collection.distinct("ticker", {"select_for_processing": 1})
    if not tickers:
        logger.info("No tickers found for processing.")
        return

    query = {
        "date": {"$gte": cutoff_date_start, "$lte": cutoff_date_end}, "ticker": {"$in": tickers}
    }
    deleted_events = []

    with qna_collection.find(query, no_cursor_timeout=True).batch_size(100) as qna_cursor:
        for qna in qna_cursor:
            qnaId = qna["_id"]
            event_id = qna["event_id"]
            ticker = qna["ticker"]
            question = qna["question"]
            answer = qna["answer"]
            if event_id not in deleted_events:
                delete_records_marked_for_deletion(event_id=event_id, coll=qna_importance_collection)
                deleted_events.append(event_id)

            # CHANGE: This is modifed to move the category collection earlier
            # Skip if qnaId is not in LLM_similarity
            # if not llm_trend_collection.find_one({"qnaId": qnaId}):
            #     continue

            # Skip if qnaId exists in qna_importance collection
            if qna_importance_collection.find_one({"qnaId": qnaId}):
                logger.info("category exists")
                continue

            # Identify sector of company by finding it from companies collection
            company_doc = companies_collection.find_one({"ticker": ticker})
            sector = company_doc["sector"] if company_doc else None
            logger.info(sector)

            # Load all Category and Importance for "sector" = sector from categories_collection
            sector_topics = []
            categories_cursor = categories_collection.find({"sector": sector}, no_cursor_timeout=True)

            sector_topics = pd.DataFrame(list(categories_cursor))

            classification_list = sector_topics["category"]

            # Generate the prompt using the separate function
            prompt = generate_category_prompt(question, answer, classification_list, sector)

            # Call the classification function (LLM or OpenAI model)
            generation = openai_service.get_completion(prompt)
            if generation:
                json_content = generation
                try:
                    category_data = json.loads(json_content)
                    qna_category = category_data.get("category", "Uncategorized")
                except json.JSONDecodeError:
                    logger.info("Invalid JSON response")
                    qna_category = "Uncategorized"
            else:
                logger.info("No JSON content found")
                qna_category = "Uncategorized"

            category_importance = "Somewhat Important"  # initialize at 0.5
            # Store the category back into MongoDB
            document = {
                "qnaId": qnaId,
                "ticker": ticker,
                "sector": sector,
                "question": question,
                "answer": answer,
                "event_id": event_id,
                "category": qna_category,
                "importance": category_importance,
                "raw result": generation,
                "date": qna["date"],
                "is_importance_assigned": False,
                "updated_at": datetime.now()
            }
            qna_importance_collection.insert_one(document)
            logger.info("Ticker: %s, QnA Category: %s, Category Importance: %s", ticker, qna_category, category_importance)

    return


# if __name__ == "__main__":
#     create_category_collection("2025-04-07", 1780)
