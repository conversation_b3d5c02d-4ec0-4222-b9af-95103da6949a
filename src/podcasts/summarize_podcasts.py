
from concurrent.futures import ThreadPoolExecutor
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.podcasts.process_status import ProcessStatus, ProcessStep


logger = get_logger(__name__)


def get_summarization_prompt(insights, speakers):
    prompt = """## Role and Context
You are an expert podcast analyst specializing in identifying shifts in speaker perspectives over time. You will analyze podcast transcript snippets to identify how important speakers have changed their views about a company. Your summary will be used by finance professionals making investment decisions, so precision, clarity, and comprehensiveness are critical.

## Task
Create a concise summary (under 100 words) of the podcast transcripts that focuses specifically on how speakers' current statements differ from their past positions about the company. Capture all significant perspective shifts that could impact investment decisions.

## Input Variables
- **TRANSCRIPT_SNIPPETS**: Relevant sections from the podcast
{transcript_snippets}

- **KEY_SPEAKERS**: List of key speakers to focus on
{key_speakers}

- **INSIGHTS**: Sentences describing what speakers said about the company previously versus their current statements
{insights_string}

## Output Requirements
- Always use direct quotes from the transcript in the summary
- Produce a paragraph-format summary (no bullet points or numbered lists)
- Highlight the two most important quotes using HTML mark tags like <mark>"quote"</mark>
- Include direct quotes from the transcript to support your analysis
- Focus exclusively on the listed important speakers, ignoring others
- Emphasize specific, meaningful changes in perspective or tone that could affect investment decisions
- Prioritize the most significant shifts in opinion
- Ensure all market-moving or financially material perspective changes are captured

## Constraints
- Keep the summary under 100 words
- Mark only the two most important quotes
- Do not repeat the same insights multiple times
- Avoid vague generalizations like "sounded better" or "seemed confident"
- Include only information directly from the provided transcripts
- Omit your own opinions or speculations about why views might have changed
- Maintain strict factual accuracy essential for financial decision-making

## Example Format
"CEO Lisa Wong has reversed her outlook on quarterly projections. While previously stating <mark>"we expect 15% growth through Q3,"</mark> she now admits "[direct quote showing reduced expectations]." Similarly, CFO Michael Chen has shifted from optimistic to cautious on cash flow, moving from "comfortable with our liquidity position" to emphasizing <mark>"[current direct quote about cash constraints]."</mark> These changes suggest potential headwinds for Q4 earnings"
"""
    key_speakers = ""
    for speaker in speakers:
        if speaker["is_important"]:
            key_speakers += f"{speaker['name']},"
    key_speakers = key_speakers.rstrip(',')

    transcript_snippets = ""
    insights_string = ""
    for _insight in insights:
        if not _insight.get("insight"):
            continue
        transcript_snippets += _insight["transcript"]["transcript"] + "\n"
        insights_string += _insight["insight"] + "\n"

    summarization_prompt = prompt.format(
        transcript_snippets=transcript_snippets,
        key_speakers=key_speakers,
        insights_string=insights_string
    )
    return summarization_prompt


def get_summarization_prompt_bullets(insights, speakers):
    prompt = """## Role and Context
You are an expert financial analyst specializing in identifying shifts in speaker perspectives over time in financial podcasts. You will analyze transcript snippets to identify how important speakers have changed their views about a company. Your analysis will be used by finance professionals for critical investment decision-making.

## Task
Create a concise, actionable summary (under 3 points) of the podcast transcripts that focuses specifically on how speakers' current statements differ from their past positions about the company's management. This summary must be optimized for quick consumption by busy financial professionals.

## Input Variables
- **TRANSCRIPT_SNIPPETS**: Relevant sections from the podcast
{transcript_snippets}

- **KEY_SPEAKERS**: List of key speakers to focus on
{key_speakers}

- **INSIGHTS**: Sentences describing what speakers said about the company previously versus their current statements
{insights_string}

## Output Requirements
- Format as concise bullet points (maximum 3 points total)
- Group all insights from the same speaker into a single comprehensive bullet point
- Include direct quote references from the original transcript for key insights
- From all quotes mentioned, highlight ONLY the two most significant quotes using HTML mark tags: "quote"
- Focus exclusively on the listed important speakers, ignoring others
- Emphasize specific, meaningful changes in perspective that could impact investment decisions

## Constraints
- Keep the summary under 3 points with each point being around 40-70 words
- Each bullet point should consolidate all relevant insights from a single speaker - never create multiple bullet points for the same speaker
- Select and mark ONLY the two most important quotes across the entire summary that represent the most significant shifts in opinion
- Avoid vague generalizations like "sounded better" or "seemed confident"
- Include only information directly from the provided transcripts
- Omit your own opinions or speculations about why views might have changed

## Example Format
- Previously expecting "15% growth," Jane Smith now states "we're revising targets downward to 5-7%" due to supply chain complications. She has also shifted from praising management to expressing concerns about leadership direction and product development timelines.

- Robert Johnson has pivoted from "prioritizing share buybacks" to emphasizing "we must conserve cash for the uncertain environment ahead." His formerly dismissive attitude toward competitors has transformed into cautious acknowledgment of market threats.

- After championing aggressive expansion strategies, Michael Williams now advocates for "consolidation and focus on core markets" while expressing diminished confidence in the previously praised product roadmap.
"""
    key_speakers = ""
    for speaker in speakers:
        if speaker["is_important"]:
            key_speakers += f"{speaker['name']},"
    key_speakers = key_speakers.rstrip(',')

    transcript_snippets = ""
    insights_string = ""
    for _insight in insights:
        if not _insight.get("insight"):
            continue
        transcript_snippets += _insight["transcript"]["transcript"] + "\n"
        insights_string += _insight["insight"] + "\n"

    summarization_prompt = prompt.format(
        transcript_snippets=transcript_snippets,
        key_speakers=key_speakers,
        insights_string=insights_string
    )
    return summarization_prompt


def summarize_podcasts():
    connection = DatabaseFactory().get_mongo_connection()
    openai_service = OpenAIService()
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")
    podcast_insights_collection = connection.get_podcast_collection("podcast_insights")

    with podcast_events_final_collection.find({"next_step": ProcessStep.SUMMARIZE_TRANSCRIPTS.value}, no_cursor_timeout=True).batch_size(30) as podcast_cursor:
        for podcast in podcast_cursor:
            speakers = podcast["speakers"]
            podcast_insights = list(podcast_insights_collection.find({"podcast_events_id": podcast["_id"]}))
            summarization_prompt = get_summarization_prompt(insights=podcast_insights, speakers=speakers)
            summarization_prompt_bullets = get_summarization_prompt_bullets(insights=podcast_insights, speakers=speakers)

            # summary = openai_service.get_completion_without_limits(prompt=summarization_prompt, temperature=0)
            # summary2 = openai_service.get_completion_without_limits(prompt=summarization_prompt_bullets, temperature=0)

            with ThreadPoolExecutor(max_workers=2) as executor:
                future1 = executor.submit(openai_service.get_completion_without_limits, prompt=summarization_prompt, temperature=0)
                future2 = executor.submit(openai_service.get_completion_without_limits, prompt=summarization_prompt_bullets, temperature=0)

                summary = future1.result()
                summary2 = future2.result()

            podcast_events_final_collection.update_one(
                {"_id": podcast["_id"]},
                {
                    "$set": {
                        "summary": summary,
                        "summary2": summary2,
                        "status": ProcessStatus.COMPLETED.value,
                        "completed_step": ProcessStep.SUMMARIZE_TRANSCRIPTS.value,
                        "show": True,
                        "next_step": None,
                    }
                }
            )
            logger.info(f"Summarizing podcast episode: {podcast['episode_title']}")


if __name__ == "__main__":
    summarize_podcasts()
