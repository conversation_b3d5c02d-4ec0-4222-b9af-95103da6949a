from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.podcasts.process_status import ProcessStatus, ProcessStep
from src.podcasts.parse_json_from_markdown import parse_json_from_markdown
from src.podcasts.get_podcast_speakers import shall_process


logger = get_logger(__name__)


def get_speaker_tagging_prompt(podcast_title, podcast_transcript: str, raw_speakers_list, speakers):
    speaker_information = ""
    speaker_id_list = ",".join(raw_speakers_list)

    for speaker in speakers:
        speaker_information += "<speaker>\n"
        speaker_information += f"- Actual Name: {speaker['name']}\n"
        speaker_information += f"- Company: {speaker['company']}\n"
        speaker_information += f"- Role: {speaker['role']}\n"
        speaker_information += f"- Speaker Type: {speaker['speaker_type']}\n"
        speaker_information += "</speaker>\n"

    speaker_tagging_prompt = f"""You are an expert podcast transcript analyst with specialized expertise in speaker identification, professional background inference, and conversational analysis. Your mission is to create a comprehensive speaker profile mapping from podcast transcripts.

## Core Objectives:
1. **Dynamic Speaker Discovery**: Identify ALL speakers actually present in the transcript, regardless of any provided speaker list
2. **Speaker Validation**: Remove any non-speaking individuals from provided lists and add any missing active speakers
3. **Professional Intelligence**: Infer each speaker's occupation and company affiliation from contextual clues in the conversation
4. **Accurate Mapping**: Create precise speaker_ID to actual name correlations

## Analysis Framework:

### Step 1: Speaker Discovery
- Systematically scan the entire transcript to identify unique speakers
- Count actual speaker_IDs present (speaker_0, speaker_1, etc.)
- Note any speakers who appear in the transcript but not in the provided list

### Step 2: Contextual Analysis
Examine these key indicators:
- **Introduction segments**: Host/guest introductions and self-presentations
- **Professional references**: Job titles, company names, industry terminology
- **Role dynamics**: Host vs. guest speaking patterns and interaction styles
- **Topic expertise**: Subject matter knowledge that reveals professional background
- **Direct mentions**: Explicit statements about roles, companies, or positions

### Step 3: Professional Background Inference
Extract occupation and company information by analyzing:
- Direct statements ("I work at...", "As a CEO of...", "In my role as...")
- Implied expertise (technical discussions, industry knowledge)
- Project or company references
- Professional achievements or experiences mentioned

### Step 4: Validation and Cross-Reference
- Verify speaker count matches actual participants
- Ensure no phantom speakers from provided lists
- Confirm professional details align with conversation context

## Input Information:
- **Podcast Title**: {podcast_title}
- **Transcript**: Complete conversation with speaker_ID tags
- ** Speaker List to Identify**: {speaker_id_list}
- **Initial Speaker List**: {speaker_information} (Note: This list may be incomplete or contain non-participants)

## Critical Instructions:
- **IGNORE** any speakers from the provided list who don't actually speak in the transcript
- **ADD** any speakers who speak in the transcript but aren't in the provided list
- **INFER** occupation and company from transcript content when not explicitly stated
- If unable to determine specific company/role, use "Unknown" but provide reasoning
- Prioritize transcript evidence over provided speaker information

## Transcript to Analyze:
{podcast_transcript}

## Required Output Format:
Provide ONLY valid JSON without explanations or additional text:

[
    {{
    "speaker_id": "speaker_0",
    "actual_name": "Full Name",
    "occupation": "Job Title/Role",
    "company": "Company Name",
    "speaker_type": "host" | "guest",
    "confidence_level": "high" | "medium" | "low",
    "evidence_summary": "Brief explanation of key evidence used for identification"
    }}
]

## Quality Assurance Checklist:
- [ ] All active speakers from transcript included
- [ ] No inactive speakers from provided list included
- [ ] Occupation inferred from context where possible
- [ ] Company information extracted or marked as "Unknown"
- [ ] Speaker types (host/guest) identified
- [ ] Confidence levels assigned based on evidence strength

Analyze the transcript now and provide the complete speaker mapping.
"""
    return speaker_tagging_prompt


def tag_speakers():
    openi_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")
    with podcast_events_final_collection.find({"next_step": ProcessStep.TAG_SPEAKERS.value}, no_cursor_timeout=True).batch_size(100) as podcast_cursor:
        for podcast in podcast_cursor:
            podcast_title = podcast["episode_title"]
            podcast_transcript = podcast["raw_transcript"]
            raw_speakers_list = podcast["raw_speakers_list"]
            speakers = podcast["speakers"]
            speaker_tagging_prompt = get_speaker_tagging_prompt(podcast_title=podcast_title, podcast_transcript=podcast_transcript, raw_speakers_list=raw_speakers_list, speakers=speakers)
            response = openi_service.get_completion_without_limits(prompt=speaker_tagging_prompt, temperature=0)
            speaker_tags = parse_json_from_markdown(response)
            tagged_podcast_transcript = podcast_transcript
            admin_mapping = {}
            for speaker in speakers:
                if speaker.get("is_admin_modified"):
                    admin_mapping[speaker["name"].lower()] = speaker["company"]

            for speaker_tag in speaker_tags:
                speaker_actual_name = speaker_tag.pop("actual_name")
                if speaker_actual_name.lower() in admin_mapping.keys():
                    speaker_tag["company"] = admin_mapping[speaker_actual_name.lower()]
                speaker_tag["name"] = speaker_actual_name  if speaker_actual_name and "unknown" not in speaker_actual_name.lower() else speaker_tag.get("speaker_id")
                speaker_tag["role"] = speaker_tag.pop("occupation")
                tagged_podcast_transcript = tagged_podcast_transcript.replace(speaker_tag["speaker_id"], speaker_tag["name"])

            process, company, speakers = shall_process(speakers=speaker_tags)
            insert_object = {}
            insert_object["speakers"] = speakers
            if process:
                if company:
                    insert_object["ticker"] = company.get("ticker")
                    insert_object["company_name"] = company["company_name"]
                    insert_object["sector"] = company.get("sector")
                insert_object["status"] = ProcessStatus.PROCESSING.value
                insert_object["completed_step"] = ProcessStep.TAG_SPEAKERS.value
                insert_object["next_step"] = ProcessStep.CHUNK_TRANSCRIPTS.value
                insert_object["transcript"] = tagged_podcast_transcript
            else:
                insert_object["status"] = ProcessStatus.DO_NOT_PROCESS.value
                insert_object["next_step"] = ProcessStatus.MOVE_TO_ADMIN.value
                insert_object["corrected_step"] = ProcessStep.TAG_SPEAKERS.value

            podcast_events_final_collection.update_one(
                {"_id": podcast["_id"]},
                {"$set": insert_object}
            )
            logger.info(f"Completed tagging speakers for: {podcast['episode_title']}")


if __name__ == "__main__":
    tag_speakers()
