from datetime import timed<PERSON><PERSON>
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.podcasts.process_status import ProcessStatus, ProcessStep
from src.podcasts.parse_json_from_markdown import parse_json_from_markdown
from src.podcasts.find_companies import find_company_in_mongodb


logger = get_logger(__name__)


def get_insights_prompt(transcript, historical_qna, speakers, company_name):
    prompt = """## Role and Objective
You are a specialized financial analyst examining podcast transcripts to identify:
1) Significant differences between current management statements and their historical positions
2) Completely new insights that management has never discussed before

Your analysis will directly inform investment decisions, so precision and investment relevance are critical.

## Input Variables
- **CURRENT_TRANSCRIPT**: Recent podcast transcript with speaker identifiers
{current_transcript}

- **HISTORICAL_QNA**: Previous management statements
{historical_qna_str}

- **TOPIC**: {category}
- **KEY_SPEAKERS**: Management/executive speakers to analyze
{key_speakers}

- **COMPANY_NAME**: The company with which the key speakers are associated
{company_name}

## Classification Framework

**Very Different**
- Significant changes with investment implications
- New information that changes business outlook, strategy, or financial projections
- Material changes in numbers, guidance, business model, or market positioning
- Example: "Market demand is strong" → "We're seeing concerning demand weakness"

**Somewhat Different**
- Moderate differences with potential investment relevance
- Some new information without material shift in overall investment thesis
- Example: "We expect modest growth" → "Growth may be slightly below previous expectations"

**New Insight**
- New business initiatives, markets, products, or strategic directions
- First-time disclosure of financial metrics or business drivers

**Similar** (No output required)
- Alignment with prior statements without new investment-relevant information

## Investment Trend Assessment
For all classifications with output, determine:
- **Uptick**: Positive implications for financial performance or investment thesis
- **Downtick**: Negative implications for financial performance or investment thesis
- **Neutral**: Mixed or unclear investment implications

## Critical Instructions
1. Analyze ONLY statements from KEY_SPEAKERS related to the specified TOPIC
2. Provide output ONLY for "Very Different," "Somewhat Different," or "New Insight" classifications
3. Return empty JSON `{{}}` if opinions remain similar with no new insights or there aren't any comments from the speaker in the transcript.
4. Extract relevant direct quotes (≈100 words each) that demonstrate the change or new information
5. Ensure your rationale explains the financial and investment implications
6. For "New Insight" classifications, omit the "reference_history" field
7. Make your insight extremely concise (max 40 words) with focus on investment implications
8. Include actual phrases used by the key speaker in quotes(") within your insight summary
9. Extract company-specific information about COMPANY_NAME relevant to the investment thesis
10. Please note that few figures like 130 are transcribed as hundred and 30
11. Prioritize for changes in opinion over new insights

## Output Format (JSON)
Return only one insight in the below format

For changes in opinion:
{{
  "reference_current": "Direct quote from current transcript (≈100 words)",
  "reference_history": "Direct quote from historical Q&A (≈100 words)",
  "classification": "Very Different OR Somewhat Different",
  "rationale": "Explanation of classification reasoning and investment implications (≈100 words)",
  "trend": "Uptick OR Downtick OR Neutral",
  "insight": "40-word summary capturing the essence of this change and its investment implications, including actual phrases from the speaker in quotes",
  "speaker": "A comma saperated list of key speaker names based on whose comments the insight is upon"
}}


For new insights:
{{
  "reference_current": "Direct quote from current transcript (≈100 words)",
  "classification": "New Insight",
  "rationale": "Explanation of why this is new and its investment implications (≈100 words)",
  "trend": "Uptick OR Downtick OR Neutral",
  "insight": "40-word summary capturing the essence of this new information and its investment implications, including actual phrases from the speaker in quotes"
  "speaker": "A comma saperated list of key speaker names based on whose comments the insight is upon"
}}

Reminder: Strictly return empty  JSON `{{}}` if
- opinions remain similar with no new insights
- or there aren't any comments from the key speakers in the transcript.
"""
    category = transcript['category']
    key_speakers = ""
    for speaker in speakers:
        if speaker["is_important"]:
            key_speakers += f"{speaker['name']},"
    key_speakers = key_speakers.rstrip(',')

    historical_qna_str = ""
    current_transcript = transcript["transcript"]
    for qna in historical_qna:
        historical_qna_str += qna.get("answer") + "\n"

    insights_prompt = prompt.format(current_transcript=current_transcript, historical_qna_str=historical_qna_str, category=category, key_speakers=key_speakers, company_name=company_name)
    return insights_prompt


def get_person_insights_prompt(transcript, speakers):
    prompt = """## Context and Role
You are a specialized business intelligence analyzer tasked with extracting precise, actionable insights from podcast transcripts. You will be analyzing discussions about specific companies to determine sentiment trends and key opinions.

## Your Task
Extract and synthesize opinions expressed by key speakers about specified entities from the provided transcript snippet. For each relevant entity mentioned, create a concise insight that captures the essence of the speaker's view on the company's business outlook.

## Input Variables
You will be provided with:
- **Transcript_Snippet**: A segment from a podcast transcript containing business discussions
{transcript_snippet}

- **Topic**: The main subject being discussed in the transcript
{category}

- **Key_Speakers**: The specific individuals whose opinions you should focus on
{key_speakers}

- **Entities**: A list of companies for which you should extract insights
{entities}

## Analysis Instructions
1. **Focus only on opinions from the Key_Speakers** about companies in the Entities list
2. **Identify sentiment direction** for each mentioned entity:
   - **Uptick**: When speakers express improved business outlook, growth potential, or increased confidence
   - **Downtick**: When speakers express concerns, challenges, declining performance, or increased uncertainty
3. **Ignore** entities from the list that aren't meaningfully discussed
4. **Extract direct quotes** (approximately 100 words) that best represent the opinion

## Output Format
Return a JSON array of objects, with each object representing one entity insight:
[
  {{
    "reference_current": "Direct quote from the transcript (approximately 100 words)",
    "trend": "Uptick OR Downtick",
    "company": "Single company name from the entities list",
    "speaker": "A comma saperated list of key speaker names based on whose comments the insight is upon",
    "insight": "40-word concise summary capturing the essence of the opinion and trend"
  }}
]


## Output Requirements
1. **Precision**: Each insight must be exactly 40 words
2. **Specificity**: Each object should focus on exactly one company
3. **Evidence-based**: Every insight must be supported by the reference quote
4. **Completeness**: Include all relevant entities from the list that received meaningful commentary
5. **Accuracy**: Correctly attribute opinions only to key speakers
6. Return empty JSON `[]` if there are substantial insights


## Example
For a transcript where Jane Smith (key speaker) discusses positive Microsoft cloud revenue growth but expresses concerns about Tesla's production delays, you might return:
[
  {{
    "reference_current": "Jane Smith: Microsoft's cloud division exceeded all expectations this quarter. Azure revenue grew by 46%, which is phenomenal in this economic climate. Their strategic focus on AI integration is paying off, and I expect this momentum to continue through next year.",
    "trend": "Uptick",
    "company": "Microsoft",
    "speaker": "Jane Smith",
    "insight": "Microsoft's cloud division showing exceptional growth with 46% Azure revenue increase. AI integration strategy is succeeding and positive momentum expected to continue."
  }},
  {{
    "reference_current": "Jane Smith: Tesla's facing significant hurdles with their production targets. The Berlin and Austin gigafactories are months behind schedule, and these delays are hurting their delivery numbers. I'm concerned about their ability to meet demand in the European market.",
    "trend": "Downtick",
    "company": "Tesla",
    "speaker": "Jane Smith",
    "insight": "Tesla experiencing production delays at Berlin and Austin gigafactories, negatively impacting delivery numbers. Concerns about meeting European market demand are growing."
  }}
]
"""
    key_speakers = ""
    for speaker in speakers:
        if speaker["is_important"]:
            key_speakers += f"{speaker['name']},"
    key_speakers = key_speakers.rstrip(',')
    category = transcript["category"]
    entities = ",".join(transcript["entities"])
    transcript_snippet = transcript["transcript"]
    person_insights_prompt = prompt.format(
        transcript_snippet=transcript_snippet,
        category=category,
        key_speakers=key_speakers,
        entities=entities
    )
    return person_insights_prompt


def generate_insights(analysis_window_days):
    """
    Generate insights from the podcast transcription.
    """
    connection = DatabaseFactory().get_mongo_connection()
    openai_service = OpenAIService()
    qna_importance_collection = connection.get_collection("qna_importance")
    podcast_events_final_collection = connection.get_podcast_collection("podcast_events_final")
    podcast_insights_collection = connection.get_podcast_collection("podcast_insights")

    sector_categories_collection = connection.get_collection("sector_categories")

    with podcast_events_final_collection.find({"next_step": ProcessStep.GATHER_INSIGHTS.value}, no_cursor_timeout=True).batch_size(20) as podcast_cursor:
        for podcast in podcast_cursor:
            try:
                ticker = podcast["ticker"]
                sector = podcast["sector"]
                podcast_date = podcast["date"]
                listennotes_id = podcast["listennotes_id"]
                analysis_end_date = podcast_date - timedelta(days=1)
                analysis_start_date = analysis_end_date - timedelta(days=analysis_window_days)
                transcripts = podcast["chunked_transcripts"]

                insights = []
                for transcript in transcripts:
                    category = transcript["category"]

                    if ticker:
                        podcast_sector_category = sector_categories_collection.find_one({"category": category, "sector": sector})
                        sector_category = podcast_sector_category["category"] if podcast_sector_category else None

                        similar_qna = list(qna_importance_collection.find({
                            "ticker": ticker,
                            "category": sector_category,
                            "date": {"$gte": analysis_start_date, "$lte": analysis_end_date}
                        }))
                        # if not similar_qna:
                        #     continue

                        insights_prompt = get_insights_prompt(
                            transcript=transcript,
                            historical_qna=similar_qna,
                            speakers=podcast["speakers"],
                            company_name=podcast["company_name"]
                        )
                        response = openai_service.get_completion_without_limits(
                            prompt=insights_prompt,
                            temperature=0,
                            response_format={"type": "json_object"}
                        )
                        insight_response = parse_json_from_markdown(response)
                        if not insight_response:
                            continue

                        insight_obj = {
                            "podcast_events_id": podcast["_id"],
                            "ticker": ticker,
                            "company_name": podcast["company_name"],
                            "category": category,
                            "date": podcast_date,
                            "listennotes_id": listennotes_id,
                            "transcript": transcript,
                            "similar_qna": similar_qna,
                            "reference_current": insight_response.get("reference_current"),
                            "reference_history": insight_response.get("reference_history"),
                            "classification": insight_response.get("classification"),
                            "rationale": insight_response.get("rationale"),
                            "trend": insight_response.get("trend"),
                            "insight": insight_response.get("insight"),
                            "speaker": insight_response.get("speaker"),
                            "is_person_tracker_insight": False
                        }
                        trend = insight_response.get("trend")
                        if isinstance(trend, str) and trend.lower() not in ["uptick", "downtick", "neutral"]:
                            continue
                        insights.append(insight_obj)
                    else:
                        person_insights_prompt = get_person_insights_prompt(
                            transcript=transcript,
                            speakers=podcast["speakers"]
                        )
                        response = openai_service.get_completion_without_limits(prompt=person_insights_prompt, temperature=0)
                        insight_response = parse_json_from_markdown(response)
                        if not insight_response:
                            continue
                        for response in insight_response:
                            if not response:
                                continue

                            company_name = response.get("company")
                            ticker = None
                            if company_name:
                                company = find_company_in_mongodb(company_name)
                                if company:
                                    company_name = company["company_name"]
                                    ticker = company["ticker"]

                            insight_obj = {
                                "podcast_events_id": podcast["_id"],
                                "ticker": ticker,
                                "company_name": company_name,
                                "category": category,
                                "date": podcast_date,
                                "listennotes_id": listennotes_id,
                                "transcript": transcript,
                                "similar_qna": None,
                                "reference_current": response.get("reference_current"),
                                "reference_history": None,
                                "classification": None,
                                "rationale": None,
                                "speaker": response.get("speaker"),
                                "trend": response.get("trend"),
                                "insight": response.get("insight"),
                                "is_person_tracker_insight": True
                            }
                            trend = insight_response.get("trend")
                            if isinstance(trend, str) and trend.lower() not in ["uptick", "downtick", "neutral"]:
                                continue
                            insights.append(insight_obj)

                if insights:
                    podcast_insights_collection.insert_many(insights)
                    podcast_events_final_collection.update_one(
                        {"_id": podcast["_id"]},
                        {
                            "$set": {
                                "status": ProcessStatus.PROCESSING.value,
                                "completed_step": ProcessStep.GATHER_INSIGHTS.value,
                                "next_step": ProcessStep.SUMMARIZE_TRANSCRIPTS.value,
                            }
                        }
                    )
                else:
                    podcast_events_final_collection.update_one(
                        {"_id": podcast["_id"]},
                        {
                            "$set": {
                                "status": ProcessStatus.DO_NOT_PROCESS.value,
                                "next_step": ProcessStatus.MOVE_TO_ADMIN.value,
                                "corrected_step": None,
                                "reason": "Could not find any insight for this podcast"
                            }
                        }
                    )

                logger.info(f"Extracted insights from podcast episode: {podcast['episode_title']}")
            except Exception as e:
                podcast_events_final_collection.update_one(
                    {"_id": podcast["_id"]},
                    {
                        "$set": {
                            "status": ProcessStatus.ERROR.value,
                            "next_step": ProcessStatus.MOVE_TO_ADMIN.value,
                            "corrected_step": None,
                            "reason": f"Error Extracting insights, Exception: {e}"
                        }
                    }
                )
                logger.exception(f"Couldnt extract insight from podcast episode: {podcast['episode_title']}")


if __name__ == "__main__":
    generate_insights(180)
