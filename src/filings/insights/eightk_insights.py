import json
import requests
from datetime import datetime, timedelta
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


logger = get_logger(__name__)


def get_eightk_insight_prompt(processed_filings):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collecton = connection.get_collection("prompts")
    prompt = prompts_collecton.find_one({
        'sector': 'software',
        'file': 'create_filing_insights',
        'function': 'create_eightk_insights'
    })
    if not prompt:
        logger.error("No prompt found for 10-Q insights.")
        raise
    prompt_eightk_insight = prompt.get('prompt', '')
    prompt_eightk_insight = prompt_eightk_insight + json.dumps(processed_filings)
    prompt_eightk_insight = prompt_eightk_insight[:300000]
    return prompt_eightk_insight


def get_eightk_summary_prompt(eightk_insight):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collecton = connection.get_collection("prompts")
    prompt = prompts_collecton.find_one({
        'prompt_name': 'eightk_insight_summary_prompt',
        'version': 1
    })
    if not prompt:
        logger.error("No prompt found for eight-k summaries")
        raise
    eightk_summary_prompt = prompt.get('prompt', '')
    eightk_summary_prompt = eightk_summary_prompt + eightk_insight
    return eightk_summary_prompt


def create_eightk_insights(cutoff_date, lookback_days):
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    filings_diff_collection = connection.get_blackberry_filings_collection("filings_diff_io")
    filings_insights_collection = connection.get_filings_collection("filings_insights_io")
    filings_summaries_collection = connection.get_filings_collection("filings_summaries_io")
    tickers = companies_collection.distinct('ticker')

    start_date = cutoff_date - timedelta(days=lookback_days)
    filings_cursor = filings_diff_collection.find({
        "uploaded_date_new": {"$gte": start_date, "$lte": cutoff_date},
        "s3_diff_url": {"$exists": True},
        "accession": {"$exists": True},
        "cik": {"$exists": True},
        "ticker": {"$in": tickers},
        "type": {"$in": ["8-K"]}
    })

    filings_count = filings_diff_collection.count_documents({
        "uploaded_date_new": {"$gte": start_date, "$lte": cutoff_date},
        "s3_diff_url": {"$exists": True},
        "accession": {"$exists": True},
        "cik": {"$exists": True},
        "ticker": {"$in": tickers},
        "type": {"$in": ["8-K"]}})
    logger.info(f"Total filings to process: {filings_count}")

    for filing in filings_cursor:
        ticker = filing["ticker"]
        type = filing["type"]
        cik = filing["cik"]
        accession = filing["accession"]
        url = filing["s3_diff_url"]
        date = filing["uploaded_date_new"]
        year_new = filing["year_new"]
        logger.info(f"Processing 8-K with Accession No: {accession}")
        if not ticker or not type or not accession:
            logger.info(f"Missing data for filing. ticker: {ticker}, type: {type}, accession: {accession}. Skipping.")
            continue
        file_name = ticker + "_" + type + "_" + accession + ".json"

        if filings_insights_collection.find_one({"accession": accession}):
            logger.info("skipping")
            continue

        if not url or not url.startswith("http"):
            logger.info(f"Invalid or missing URL for filing {file_name}. Skipping.")
            continue

        response = requests.get(url)

        if response.status_code == 404:
            logger.error(f"File not found at {url}. Skipping.")
            continue

        if not response or response.status_code != 200:
            logger.info(f"Failed to fetch {url}. Skipping.")
            continue

        if response.status_code != 200:
            logger.info(f"Request to {url} returned status code {response.status_code}. Skipping.")
            continue

        try:
            processed_filings = response.json()
        except requests.exceptions.JSONDecodeError:
            logger.error(f"Unable to decode JSON for {file_name}. Skipping.")
            continue

        eightk_insight_prompt = get_eightk_insight_prompt(processed_filings=processed_filings)

        eightk_insight = openai_service.get_completion(prompt=eightk_insight_prompt, system_prompt="You are a helpful assistant who reads and extracts information. Please respond in a json", temperature=0, max_token=1000)
        insights_list = parse_json_from_markdown(eightk_insight)

        if isinstance(insights_list, dict):
            insights_list = [insights_list]

        # Ensure insights_list is a list of dictionaries
        if not isinstance(insights_list, list):
            logger.info(f"Unexpected JSON structure for {file_name}. Skipping.")
            continue

        if not insights_list:
            logger.info(f"No insights found for {file_name}. Skipping.")
            continue

        # Add ticker and file_name to each document
        for insight in insights_list:
            if not isinstance(insight, dict):
                logger.info(f"Unexpected insight format in {file_name}. Skipping.")
                continue
            insight.update({
                "ticker": ticker,
                "file_name": file_name,
                "uploaded_date_new": date,
                "cik": cik,
                "accession": accession,
                "show": True,
                "year_new": year_new,
                "type": type,
                "updated_at": datetime.now()
            })

        filings_insights_collection.insert_many(insights_list)
        prompt_summary = get_eightk_summary_prompt(eightk_insight=eightk_insight)
        filing_summary = openai_service.get_completion(prompt=prompt_summary, system_prompt="You are a helpful assistant who is NOT verbose and answers in text under 70 words without bullets or numbers.", temperature=0, max_token=300)
        filings_summaries_collection.update_one(
            {"ticker": ticker, "file_name": file_name, "cik": cik, "accession": accession},
            {"$set": {"summary": filing_summary, "updated_at": datetime.now(), "show": True, "type": type, "year_new": year_new, "uploaded_date_new": date}},
            upsert=True
        )
        filings_diff_collection.update_one({"_id": filing["_id"]}, {"$set": {"insights_created": True}})
        logger.info(f"Completed Processing 8-K with Accession No: {accession}")


if __name__ == "__main__":
    CUTOFF_DATE = datetime.now()
    LOOKBACK_DAYS = 1
    create_eightk_insights(cutoff_date=CUTOFF_DATE, lookback_days=LOOKBACK_DAYS)
