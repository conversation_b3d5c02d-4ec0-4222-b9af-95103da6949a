import json
import requests
from datetime import datetime, timedelta
from src.core.logging import get_logger
from src.database.factory import DatabaseFactory
from src.services.llm.openai_service import OpenAIService
from src.helpers.parse_json_from_markdown import parse_json_from_markdown


logger = get_logger(__name__)


def get_proxy_insight_prompt(processed_filings):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collecton = connection.get_collection("prompts")
    prompt = prompts_collecton.find_one({'sector': 'software', 'file': 'create_filing_insights', 'function': 'create_proxy_insights'})
    if not prompt:
        logger.error("No prompt found for proxy insights.")
        raise
    proxy_insight_prompt = prompt.get('prompt', '')
    proxy_insight_prompt = proxy_insight_prompt + json.dumps(processed_filings)
    proxy_insight_prompt = proxy_insight_prompt[:300000]
    return proxy_insight_prompt


def get_exec_comp_cushion_insight_prompt(processed_filings, mda_text):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collecton = connection.get_collection("prompts")
    prompt = prompts_collecton.find_one({'sector': 'software', 'file': 'create_filing_insights', 'function': 'create_proxy_comp_data'})
    if not prompt:
        logger.info("No prompt found for exec comp custom insights.")
        raise
    get_exec_comp_cushion_prompt = prompt.get('prompt', '')
    get_exec_comp_cushion_prompt = get_exec_comp_cushion_prompt + json.dumps(processed_filings) + mda_text
    get_exec_comp_cushion_prompt = get_exec_comp_cushion_prompt[:300000]
    return get_exec_comp_cushion_prompt


def get_proxy_summary_prompt(proxy_insights, exec_comp_cushion_insight):
    connection = DatabaseFactory().get_mongo_connection()
    prompts_collecton = connection.get_collection("prompts")
    prompt = prompts_collecton.find_one({
        'prompt_name': 'proxy_insight_summary_prompt',
        'version': 1
    })
    if not prompt:
        logger.error("No prompt found for proxy insight summaries")
        raise
    prompt_summary = prompt.get('prompt', '')
    prompt_summary = prompt_summary + "insights" + proxy_insights + "compensation targets" + exec_comp_cushion_insight
    return prompt


def create_proxy_insights(cutoff_date, lookback_days):
    openai_service = OpenAIService()
    connection = DatabaseFactory().get_mongo_connection()
    companies_collection = connection.get_collection("companies")
    qna_collection = connection.get_collection("qnas")
    filings_diff_collection = connection.get_blackberry_filings_collection("filings_diff_io")
    filings_insights_collection = connection.get_filings_collection("filings_insights_io")
    filings_summaries_collection = connection.get_filings_collection("filings_summaries_io")
    management_targets_collection = connection.get_filings_collection("management_targets")
    tickers = companies_collection.distinct('ticker')
    start_date = cutoff_date - timedelta(days=lookback_days)
    filings_cursor = filings_diff_collection.find({
        "uploaded_date_new": {"$gte": start_date, "$lte": cutoff_date},
        "s3_diff_url": {"$exists": True},
        "accession": {"$exists": True},
        "cik": {"$exists": True},
        "ticker": {"$in": tickers},
        "type": "DEF 14A"

    })
    for filing in filings_cursor:
        ticker = filing["ticker"]
        type = filing["type"]
        cik = filing["cik"]
        accession = filing["accession"]
        url = filing["s3_diff_url"]
        date = filing["uploaded_date_new"]
        year_new = filing["year_new"]
        year_old = filing["year_old"]
        logger.info(f"Processing Proxy Insights with Accession No: {accession}")
        if not ticker or not type or not accession:
            logger.info(f"Missing data for filing. ticker: {ticker}, type: {type}, accession: {accession}. Skipping.")
            continue
        file_name = ticker + "_" + type + "_" + accession + ".json"

        if filings_insights_collection.find_one({"accession": accession}):
            logger.info("skipping")
            continue

        if not url or not url.startswith("http"):
            logger.info(f"Invalid or missing URL for filing {file_name}. Skipping.")
            continue

        response = requests.get(url)

        if response.status_code != 200:
            logger.info(f"Request to {url} returned status code {response.status_code}. Skipping.")
            continue

        try:
            processed_filings = response.json()
        except requests.exceptions.JSONDecodeError:
            logger.info(f"Unable to decode JSON for {file_name}. Skipping.")
            continue

        if len(processed_filings) == 0 and not processed_filings:
            logger.info(f"No processed filings found for {file_name}. Skipping.")
            continue

        proxy_insight_prompt = get_proxy_insight_prompt(processed_filings=processed_filings)

        proxy_insights = openai_service.get_completion(prompt=proxy_insight_prompt, system_prompt="You are a helpful assistant who reads and extracts information. Please respond in a json", temperature=0, max_token=1000)
        insights_list = parse_json_from_markdown(proxy_insights)

        if isinstance(insights_list, dict):
            insights_list = [insights_list]

        # Ensure insights_list is a list of dictionaries
        if not isinstance(insights_list, list):
            logger.info(f"Unexpected JSON structure for {file_name}. Skipping.")
            continue

        if not insights_list:
            logger.info(f"No insights found for {file_name}. Skipping.")
            continue

        # Add ticker and file_name to each document
        for insight in insights_list:
            if not isinstance(insight, dict):
                logger.info(f"Unexpected insight format in {file_name}. Skipping.")
                continue
            insight.update({
                "ticker": ticker,
                "file_name": file_name,
                "uploaded_date_new": date,
                "cik": cik,
                "accession": accession,
                "show": True,
                "year_new": year_new,
                "year_old": year_old,
                "type": type,
                "updated_at": datetime.now()
            })

        filings_insights_collection.insert_many(insights_list)

        # Find MDA section from event with title containing Q4 for the same ticker and year
        mda_sections = qna_collection.find({
            "ticker": ticker,
            "event": {"$regex": "Q4 ", "$options": "i"},
            "section": "MDA"
        })

        mda_text = ""
        for section in mda_sections:
            mda_text += section.get("answer", "") + "\n"

        exec_comp_cushion_insight_prompt = get_exec_comp_cushion_insight_prompt(processed_filings=processed_filings, mda_text=mda_text)
        exec_comp_cushion_insight = openai_service.get_completion(prompt=exec_comp_cushion_insight_prompt, system_prompt="You are a helpful assistant who reads and extracts information. Please respond in a json", temperature=0, max_token=1000)
        management_targets = parse_json_from_markdown(exec_comp_cushion_insight)

        # Normalize to a list so we can handle both dict and list uniformly
        if isinstance(management_targets, dict):
            docs = [management_targets]
        elif isinstance(management_targets, list):
            docs = management_targets
        else:
            logger.info(f"Unexpected JSON structure for {file_name}. Skipping.")
            continue

        for doc in docs:
            if not isinstance(doc, dict):
                logger.info(f"Skipping non‑dict entry in {file_name}")
                continue

            doc.update({
                "ticker": filing["ticker"],
                "type": filing["type"],
                "cik": filing["cik"],
                "accession": filing["accession"],
                "url": filing["s3_diff_url"],
                "uploaded_date_new": filing["uploaded_date_new"],
                "year_new": filing["year_new"],
                "year_old": filing["year_old"],
                "updated_at": datetime.now()
            })
            management_targets_collection.insert_one(doc)

        prompt_summary = get_proxy_summary_prompt(proxy_insights, exec_comp_cushion_insight)
        filing_summary = openai_service.get_completion(prompt=prompt_summary, system_prompt="You are a helpful assistant who is NOT verbose and answers in text under 70 words without bullets or numbers.", temperature=0, max_token=300)
        filings_summaries_collection.update_one(
            {"ticker": ticker, "file_name": file_name, "cik": cik, "accession": accession},
            {"$set": {"summary": filing_summary, "updated_at": datetime.now(), "show": True, "type": "DEF 14A", "year_new": year_new, "year_old": year_old, "uploaded_date_new": date}},
            upsert=True
        )

        filings_diff_collection.update_one({"_id": filing["_id"]}, {"$set": {"insights_created": True}})
        logger.info(f"Completed Processing Proxy Insights with Accession No: {accession}")


if __name__ == "__main__":
    CUTOFF_DATE = datetime.now()
    LOOKBACK_DAYS = 3
    create_proxy_insights(cutoff_date=CUTOFF_DATE, lookback_days=LOOKBACK_DAYS)
