"""
Stores constants across the application
"""
import os
from dotenv import load_dotenv

load_dotenv()

APP_NAME = "SlatedBackend"
VERSION = "1.0.0"

# Logging
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
LOG_DIRECTORY = os.getenv('LOG_DIRECTORY', 'logs')

# App Configuration
LOOKBACK_DAYS = int(os.getenv('LOOKBACK_DAYS', 1))
REPVUE_PROCESSING_WINDOW = int(os.getenv('REPVUE_PROCESSING_WINDOW', 10))
BLIND_PROCESSING_WINDOW = int(os.getenv('BLIND_PROCESSING_WINDOW', 10))
TRUSTPILOT_PROCESSING_WINDOW = int(os.getenv('TRUSTPILOT_PROCESSING_WINDOW', 10))
FILINGS_LOOKBACK_DAYS = int(os.getenv('FILINGS_LOOKBACK_DAYS', 1))
SLIDES_LOOKBACK_DAYS = int(os.getenv('SLIDES_LOOKBACK_DAYS', 3))

# MongoDB
MONGO_URI = os.getenv('MONGO_URI')
MONGO_BLACKBERRY_URI = os.getenv('MONGO_BLACKBERRY_URI')
MONGO_MAX_POOL_SIZE = int(os.getenv('MONGO_MAX_POOL_SIZE', 50))
MONGO_MIN_POOL_SIZE = int(os.getenv('MONGO_MIN_POOL_SIZE', 2))
MONGO_MAX_IDLE_TIME_MS = int(os.getenv('MONGO_MAX_IDLE_TIME_MS', 300000))


# MongoDB collections
BLACKBERRY_SALES_DB_NAME = os.getenv('BLACKBERRY_SALES_DB_NAME')
BLACKBERRY_EMPLOYEE_DB_NAME = os.getenv('BLACKBERRY_EMPLOYEE_DB_NAME')
BLACKBERRY_PRODUCT_DB_NAME = os.getenv('BLACKBERRY_PRODUCT_DB_NAME')
BLACKBERRY_FILINGS_DB_NAME = os.getenv("BLACKBERRY_FILINGS_DB_NAME")
MONGO_DB_NAME = os.getenv('MONGO_DB_NAME')
PODCAST_DB_NAME = os.getenv('PODCAST_DB_NAME')
MONGO_STOCK_DB_NAME = os.getenv('MONGO_STOCK_DB_NAME')
MONGO_EMAIL_DB_NAME = os.getenv('MONGO_EMAIL_DB_NAME')
SALES_INSIGHT_DB_NAME = os.getenv('SALES_INSIGHT_DB_NAME')
PRODUCT_INSIGHT_DB_NAME = os.getenv('PRODUCT_INSIGHT_DB_NAME')
EMPLOYEE_INSIGHT_DB_NAME = os.getenv('EMPLOYEE_INSIGHT_DB_NAME')
FILINGS_DB_NAME = os.getenv('FILINGS_DB_NAME')
FEED_DB_NAME = os.getenv('FEED_DB_NAME')
SLIDES_DB_NAME = os.getenv('SLIDES_DB_NAME')

# Snowflake
SNOW_USER = os.getenv('SNOW_USER')
SNOW_PASSWORD = os.getenv('SNOW_PASSWORD')
SNOW_ACCOUNT = os.getenv('SNOW_ACCOUNT')
SNOW_WAREHOUSE = os.getenv('SNOW_WAREHOUSE')
SNOW_DATABASE = os.getenv('SNOW_DATABASE')
SNOW_SCHEMA = os.getenv('SNOW_SCHEMA')

# S3
S3_BUCKET_NAME = os.getenv('S3_BUCKET_NAME')
S3_PODCASTS_BUCKET_NAME = os.getenv('S3_PODCASTS_BUCKET_NAME')
S3_ACCESS_KEY = os.getenv('S3_AWS_ACCESS_KEY_ID')
S3_SECRET_KEY = os.getenv('S3_AWS_SECRET_ACCESS_KEY')
S3_REGION = os.getenv('S3_AWS_REGION')

# Slides S3 Client
SLIDES_S3_ACCESS_KEY_ID = os.getenv('SLIDES_S3_AWS_ACCESS_KEY_ID')
SLIDES_S3_SECRET_ACCESS_KEY = os.getenv('SLIDES_S3_AWS_SECRET_ACCESS_KEY')
SLIDES_S3_REGION = os.getenv('SLIDES_S3_AWS_REGION')
SLIDES_S3_BUCKET_NAME = os.getenv('SLIDES_S3_BUCKET_NAME')

# BEDROCK
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.getenv('AWS_REGION')

# OpenAI
OPENAI_AZURE_ENDPOINT_EAST_US = os.getenv("OPENAI_AZURE_ENDPOINT_EAST_US")
OPENAI_AZURE_API_VERSION_EAST_US = os.getenv("OPENAI_AZURE_API_VERSION_EAST_US")
OPENAI_AZURE_KEY_EAST_US = os.getenv("OPENAI_AZURE_KEY_EAST_US")

# SP Global
SP_USERNAME = os.getenv('SP_USERNAME')
SP_PASSWORD = os.getenv('SP_PASSWORD')

# Listennotes API
LISTENNOTES_API_KEY = os.getenv('LISTENNOTES_API_KEY')

# Gemini API Key
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')

# Vertex API Key
VERTEX_PROJECT_ID = os.getenv("VERTEX_PROJECT_ID")
VERTEX_LOCATION = os.getenv("VERTEX_LOCATION")
VERTEX_CREDENTIALS_PATH = os.getenv("VERTEX_CREDENTIALS_PATH")

# Deepgram API Key
DEEPGRAM_API_KEY = os.getenv('DEEPGRAM_API_KEY')

# Sentry
RELEASE = os.getenv("RELEASE")
SENTRY_DSN = os.getenv("SENTRY_DSN")
ENVIRONMENT = os.getenv("ENVIRONMENT", "production")

# SEC API Key
SEC_API_KEY = os.getenv('SEC_API_KEY')
